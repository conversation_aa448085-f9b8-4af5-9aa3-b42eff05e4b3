# Search and Pagination Fix

## Issue Description
When searching in the TextToAudioUsageHistoryTable and then changing pagination, the table would show the correct search results initially, but then automatically reload and show the first page rows without the search filter applied. This happened only the first time after searching.

## Root Cause
The issue was in the `SimpleCrudFilter` component's `componentDidUpdate` method. When pagination routing changed (page number or page size), it would call `onChangeFilter` directly without considering the `queryFilter` state management that was designed to handle search query changes.

The sequence of events was:
1. User searches → `handleSearchQueryChange` sets `queryFilter: true` and calls `onChangeFilter` with search query
2. User changes pagination → `setPageNumber(1)` is called, which updates the URL
3. `componentDidUpdate` detects pagination change and calls `onChangeFilter` with current filter (including search query)
4. Later, some other filter change happens → `handleFilterChange` sees `queryFilter: true`, sets it to `false`, but doesn't call `onChangeFilter`
5. This could cause the search query to be lost in subsequent updates

## Solution
Modified the `componentDidUpdate` method in `SimpleCrudFilter.js` to:
1. Not interfere with the `queryFilter` state when pagination routing changes
2. Call `onChangeFilter` directly with updated pagination fields while preserving the search query
3. Added comments to clarify the intent

Also fixed a potential issue in `TableFilterInput.tsx` where the local state wasn't syncing with prop changes by adding a `useEffect` to update the local query state when the value prop changes.

## Files Modified
1. `src/common/components/dataViews/SimpleCrud/SimpleCrudFilter/SimpleCrudFilter.js`
   - Updated `componentDidUpdate` method to preserve search query during pagination changes
   
2. `src/common/components/dataViews/Tables/Table/TableHeader/TableFilterInput.tsx`
   - Added `useEffect` to sync local state with prop value changes
   - Added `useEffect` import

## Testing
To test the fix:
1. Navigate to the Text-To-Audio Usage History page
2. Enter a search query
3. Change the pagination (go to page 2, 3, etc.)
4. Verify that the search results are maintained and the table doesn't reload with unfiltered data
5. Try changing page size as well to ensure it works in both scenarios

## Expected Behavior After Fix
- Search query should be preserved when changing pagination
- No automatic reload showing first page rows after pagination change
- Search functionality should work consistently across all pagination operations
