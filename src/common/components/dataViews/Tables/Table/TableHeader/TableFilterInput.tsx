import React, { useCallback, useState, useEffect } from 'react';

import styles from './styles.scss';
import SearchField from '../../../../controls/base/SearchField';
import usePaginationRouting from '../../../../../data/hooks/usePaginationRouting';

interface ITableFilterInput {
  value: string;
  onChange: (v: string) => void;
  hasPaginationRouting?: boolean;
}

const TableFilterInput = ({
  value,
  onChange,
  hasPaginationRouting = false,
}: ITableFilterInput) => {
  const [query, setQuery] = useState(value);

  const { setPageNumber } = usePaginationRouting();

  // Sync local state with prop value to handle external changes
  useEffect(() => {
    setQuery(value);
  }, [value]);

  const handleChange = useCallback(
    query => {
      setQuery(query);
      const preparedSearchQuery = (query || '').trim();
      onChange(preparedSearchQuery);
      hasPaginationRouting && setPageNumber(1);
    },
    [onChange, setPageNumber, hasPaginationRouting],
  );

  return (
    <div className={styles.SearchFieldLeftSpace}>
      <SearchField
        isInTable
        value={query}
        widthClassName="col-lg-3 col-md-4 col-sm-4 col-xs-4"
        onChange={handleChange}
      />
    </div>
  );
};

export default TableFilterInput;
